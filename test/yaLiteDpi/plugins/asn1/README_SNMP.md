# SNMP ASN.1 Parser Implementation

## Overview

This document describes the implementation of an SNMP (Simple Network Management Protocol) parser using ASN.1 code generation with asn1c. The parser is integrated into the yaLiteDpi dissector framework and can parse SNMP v1 messages according to RFC 1157.

## Architecture

### ASN.1 Code Generation

The parser uses asn1c to generate C code from ASN.1 definitions:

```
ASN.1 Definition (snmp.asn) → asn1c → Generated C Code → Static Library → Plugin
```

### Directory Structure

```
test/yaLiteDpi/plugins/asn1/
├── CMakeLists.txt                    # Build configuration for ASN.1 parsers
├── protocols/
│   └── snmp/
│       └── snmp.asn                  # SNMP ASN.1 definition (RFC 1157)
├── test_asn1.c                       # Standalone ASN.1 test program
└── test_snmp_dissector.c             # Unit tests for SNMP dissector

build/test/yaLiteDpi/plugins/asn1/
├── generated/
│   └── snmp/                         # Generated ASN.1 C code
│       ├── Message.c/.h              # Main SNMP message structure
│       ├── PDUs.c/.h                 # PDU choice types
│       ├── PDU.c/.h                  # Basic PDU structure
│       ├── VarBind.c/.h              # Variable binding
│       ├── ObjectSyntax.c/.h         # Object syntax types
│       └── ...                       # Other generated files
└── libasn1_snmp.a                    # Static library with all ASN.1 code
```

## ASN.1 Definition

The SNMP ASN.1 definition is based on RFC 1157 and includes:

### Core Message Structure
```asn1
Message ::= SEQUENCE {
    version INTEGER { version-1(0) },
    community OCTET STRING,
    data PDUs
}
```

### PDU Types
```asn1
PDUs ::= CHOICE {
    get-request [0] IMPLICIT PDU,
    get-next-request [1] IMPLICIT PDU,
    get-response [2] IMPLICIT PDU,
    set-request [3] IMPLICIT PDU,
    trap [4] IMPLICIT Trap-PDU
}
```

### Variable Bindings
```asn1
VarBind ::= SEQUENCE {
    name ObjectName,
    value ObjectSyntax
}

ObjectSyntax ::= CHOICE {
    simple SimpleSyntax,
    application-wide ApplicationSyntax
}
```

## Build System Integration

### CMake Configuration

The build system automatically:
1. Runs asn1c to generate C code from ASN.1 definitions
2. Compiles generated code into a static library
3. Links the library with the SNMP dissector plugin

### Key CMake Features
- Automatic dependency tracking for ASN.1 files
- Regeneration when ASN.1 definitions change
- Static linking to avoid symbol conflicts
- Support for multiple protocol parsers

## Dissector Implementation

### Core Functions

1. **snmp_dissect()**: Main parsing function
   - Validates minimum packet length
   - Decodes ASN.1 using ber_decode()
   - Extracts message fields
   - Processes variable bindings

2. **process_varbind_list()**: Variable binding processor
   - Handles different value types (integer, string, OID, NULL)
   - Converts OIDs to string representation
   - Extracts application-specific types

3. **Helper Functions**:
   - `oid_to_string()`: Converts OID to dotted notation
   - `octet_string_to_string()`: Safely converts OCTET STRING

### Supported Features

- **SNMP v1 Messages**: GetRequest, GetNextRequest, GetResponse, SetRequest, Trap
- **Data Types**: INTEGER, OCTET STRING, OBJECT IDENTIFIER, NULL
- **Application Types**: NetworkAddress, Counter, Gauge, TimeTicks, Opaque
- **Error Handling**: Graceful handling of malformed packets
- **Memory Management**: Proper cleanup of ASN.1 structures

## Testing

### Unit Tests

The implementation includes comprehensive unit tests:

```bash
# Compile and run unit tests
gcc -I build/test/yaLiteDpi/plugins/asn1/generated/snmp \
    test/yaLiteDpi/plugins/test_snmp_dissector.c \
    build/test/yaLiteDpi/plugins/asn1/libasn1_snmp.a \
    -o test_snmp_dissector && ./test_snmp_dissector
```

### Integration Tests

Real-world testing with pcap files:

```bash
# Test with SNMP traffic
./bin/yaLiteDpi -r test/pcaps/b6300a.cap | grep snmp
```

## Performance Considerations

1. **Static Linking**: All ASN.1 symbols are statically linked to avoid runtime symbol resolution
2. **Memory Efficiency**: Structures are freed immediately after processing
3. **Error Handling**: Fast failure for invalid packets
4. **Minimal Copying**: Direct access to packet data where possible

## Troubleshooting

### Common Issues

1. **Undefined Symbols**: Ensure ASN.1 library is properly linked
2. **Parse Failures**: Check ASN.1 definition for tag conflicts
3. **Memory Leaks**: Verify ASN_STRUCT_FREE() calls

### Debug Tools

1. **Standalone Parser**: Use test_asn1.c for isolated testing
2. **Hex Dump**: Enable debug output to examine raw packet data
3. **ASN.1 Validation**: Use asn1c with -E flag for detailed error messages

## Future Enhancements

1. **SNMP v2c/v3 Support**: Extend ASN.1 definitions
2. **Performance Optimization**: Implement partial parsing for large packets
3. **Additional Data Types**: Support for Counter64, Unsigned32, etc.
4. **MIB Integration**: Add OID name resolution

## References

- RFC 1157: Simple Network Management Protocol (SNMP)
- ASN.1 ITU-T X.680 series standards
- asn1c documentation: https://github.com/vlm/asn1c
