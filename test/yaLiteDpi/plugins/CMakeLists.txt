cmake_minimum_required(VERSION 3.14)

# find_package(yaEngineNext REQUIRED)
include(${CMAKE_SOURCE_DIR}/cmake/yaEngineNextConfig.cmake)

# Add ASN.1 subdirectory
add_subdirectory(asn1)

#
# plugins
#
addEngineNextPlugin(udp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_udp.c
)

addEngineNextPlugin(rtp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_rtp.c
)

addEngineNextPlugin(dns ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_dns.c
)

# addEngineNextPlugin(hwzzeth ${CMAKE_SOURCE_DIR}/bin/plugins
#   SOURCES dissector_hwzzeth.c
# )

addEngineNextPlugin(hwzz ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_hwzz.c
)

addEngineNextPlugin(rt ${CMAKE_SOURCE_DIR}/bin/plugins
  TRAILER
  SOURCES dissector_trailer_rt.c
)

addEngineNextPlugin(sip ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES       dissector_sip.c
  RAGEL_SOURCES parser_sip.rl
)

addEngineNextPlugin(snmp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_snmp.c
  LINK_LIBRARIES "-Wl,--whole-archive" asn1_snmp "-Wl,--no-whole-archive"
)
