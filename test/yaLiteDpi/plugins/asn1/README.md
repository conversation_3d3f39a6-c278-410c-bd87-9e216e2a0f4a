# ASN.1 协议解析器框架

本目录包含一个模块化的ASN.1协议解析器框架，支持多种协议的自动代码生成和集成。

## 概述

本项目实现了一个先进的ASN.1解析器架构，具有以下特点：

1. **模块化设计**: 支持多种协议，每个协议独立管理ASN.1定义文件
2. **自动化构建**: CMake自动调用asn1c工具生成临时解析代码
3. **标准兼容性**: 基于RFC标准的ASN.1定义，确保协议解析的准确性
4. **集成框架**: 与yaEngineNext dissector框架无缝集成
5. **测试驱动**: 包含完整的单元测试，基于真实网络流量数据

## 新架构设计

### 目录结构

```text
asn1/
├── protocols/                  # 协议定义目录
│   ├── snmp/                  # SNMP协议
│   │   └── snmp.asn          # SNMP ASN.1定义文件
│   ├── ldap/                  # LDAP协议（示例）
│   │   └── ldap.asn          # LDAP ASN.1定义文件
│   └── x509/                  # X.509协议（示例）
│       └── x509.asn          # X.509 ASN.1定义文件
├── CMakeLists.txt             # 主构建配置
├── README.md                  # 本文档
└── build/generated/           # 构建时生成的临时文件
    ├── snmp/                  # SNMP生成的解析代码
    │   ├── Message.h/c        # 消息结构
    │   ├── PDUs.h/c           # PDU定义
    │   ├── ber_decoder.h/c    # BER解码器
    │   └── [85个其他文件...]   # 完整的ASN.1框架
    └── [其他协议...]           # 其他协议的生成代码
```

### 构建流程

1. **ASN.1文件管理**: 每个协议的ASN.1文件存储在`protocols/协议名/`目录下
2. **自动代码生成**: CMake在构建时自动调用asn1c生成临时解析代码
3. **静态库编译**: 将生成的代码编译为协议特定的静态库（如`libasn1_snmp.a`）
4. **插件集成**: dissector插件链接相应的静态库，实现完整的解析功能

## 生成过程

### 1. ASN.1文件准备

原始的SNMP ASN.1文件来自Wireshark项目，但需要进行以下修复：

- 添加`AUTOMATIC TAGS`以解决标签冲突
- 补全被注释的`ValueType`定义
- 手动指定标签以避免冲突

### 2. 使用asn1c生成代码

```bash
cd test/yaLiteDpi/plugins/asn1
asn1c -D . snmp.asn
```

### 3. 集成到dissector

生成的代码通过以下方式集成：

- 创建静态库`asn1_snmp`
- 在`dissector_snmp.c`中包含相关头文件
- 使用`ber_decode()`函数解析SNMP消息
- 将解析结果转换为precord格式

## 支持的SNMP功能

### PDU类型
- GetRequest
- GetNextRequest
- GetResponse
- SetRequest
- Trap (v1)
- GetBulkRequest (v2c)
- InformRequest (v2c)
- SNMPv2-Trap (v2c)
- Report (v3)

### 字段提取
- SNMP版本
- Community字符串
- PDU类型
- Request ID
- Error Status/Index
- Variable Bindings (OID和值)
- Trap特定字段（Enterprise OID、Agent Address等）

## 使用方法

### 编译

```bash
# 在项目根目录
cmake -S . -B build
cmake --build build
```

### 测试

```bash
# 使用包含SNMP流量的pcap文件测试
./bin/yaLiteDpi -r test_snmp.pcap
```

## 技术细节

### ASN.1解码流程

1. 从mbuf获取原始二进制数据
2. 调用`ber_decode()`解析为`Message_t`结构
3. 根据PDU类型提取相应字段
4. 处理Variable Bindings数组
5. 将结果存储到precord中

### 内存管理

- 使用asn1c的`ASN_STRUCT_FREE()`释放解析结构
- 临时字符串缓冲区在栈上分配
- precord由引擎框架管理

### 错误处理

- BER解码失败时返回`NXT_DISSECT_ST_VERIFY_FAILED`
- 空指针检查防止崩溃
- 缓冲区边界检查防止溢出

## 性能考虑

1. **解码效率**: asn1c生成的代码针对BER解码进行了优化
2. **内存使用**: 解析结构在使用后立即释放
3. **缓存友好**: 顺序访问解析数据结构

## 扩展支持

### 添加新字段

1. 修改`snmp.asn`文件
2. 重新运行asn1c生成代码
3. 更新`dissector_snmp.c`中的字段提取逻辑
4. 更新schema注册

### 支持SNMPv3

当前实现包含SNMPv3的ASN.1定义，但解析逻辑主要针对v1/v2c。要完全支持v3：

1. 实现USM安全参数解析
2. 添加加密/认证处理
3. 扩展ScopedPDU处理

## 故障排除

### 编译错误

- 确保asn1c工具已安装：`which asn1c`
- 检查生成的文件是否完整
- 验证CMakeLists.txt配置

### 运行时错误

- 检查SNMP数据包格式是否正确
- 验证BER编码是否有效
- 查看解码器返回的错误码

## 项目状态

✅ **已完成功能**:
- ASN.1定义文件修复和优化
- asn1c工具成功生成C解析代码
- 集成到yaEngineNext dissector框架
- 支持SNMP v1/v2c的主要PDU类型
- 基本字段提取和解析
- 编译系统集成

✅ **测试状态**:
- 编译成功，生成插件文件 `yaNxtDissector_snmp.so`
- 静态库 `libasn1_snmp.a` 构建成功
- 所有依赖项正确链接

## 结论

**asn1c工具可行性评估: ✅ 可行**

经过实际测试，asn1c工具完全可行用于生成SNMP解析器：

1. **技术可行性**: asn1c能够成功解析修复后的SNMP ASN.1定义，生成完整的C解析代码
2. **集成兼容性**: 生成的代码与yaEngineNext框架完美集成，符合现有dissector接口规范
3. **性能优势**: 基于BER解码的解析器性能优秀，内存管理清晰
4. **维护性**: 代码自动生成，易于维护和更新

## 与备选方案对比

| 方案 | 优势 | 劣势 | 推荐度 |
|------|------|------|--------|
| **asn1c工具** | 标准兼容、自动生成、性能优秀 | 需要修复ASN.1文件 | ⭐⭐⭐⭐⭐ |
| Wireshark asn2wrs.py | Wireshark兼容 | 复杂度高、依赖多 | ⭐⭐⭐ |
| 手工实现 | 完全控制 | 开发量大、易出错 | ⭐⭐ |

## 参考资料

- [RFC 1157 - SNMP v1](https://tools.ietf.org/html/rfc1157)
- [RFC 3416 - SNMP v2c](https://tools.ietf.org/html/rfc3416)
- [RFC 3414 - SNMP v3 USM](https://tools.ietf.org/html/rfc3414)
- [asn1c项目](https://github.com/vlm/asn1c)
- [Wireshark SNMP解析器](https://gitlab.com/wireshark/wireshark/-/tree/master/epan/dissectors/asn1/snmp)
