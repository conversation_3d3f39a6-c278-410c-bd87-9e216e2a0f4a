#include <gtest/gtest.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_mbuf.h>
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/allocator.h>

// Test data extracted from tshark output of b6300a.cap
// Frame 1: SNMP GetRequest
static const uint8_t snmp_get_request_data[] = {
    // SNMP Message: version=0, community="public", get-request
    0x30, 0x2e,                                     // SEQUENCE, length 46
    0x02, 0x01, 0x00,                               // INTEGER version-1 (0)
    0x04, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, // OCTET STRING "public"
    0xa0, 0x21,                                     // GetRequest-PDU, length 33
    0x02, 0x01, 0x26,                               // INTEGER request-id (38)
    0x02, 0x01, 0x00,                               // INTEGER error-status (0)
    0x02, 0x01, 0x00,                               // INTEGER error-index (0)
    0x30, 0x16,                                     // SEQUENCE variable-bindings, length 22
    0x30, 0x14,                                     // SEQUENCE VarBind, length 20
    0x06, 0x0a, 0x2b, 0x06, 0x01, 0x02, 0x01, 0x01, 0x02, 0x00, // OID 1.3.6.1.2.1.1.2.0
    0x05, 0x00                                      // NULL value
};

// Frame 2: SNMP GetResponse (partial data for testing)
static const uint8_t snmp_get_response_data[] = {
    // SNMP Message: version=0, community="public", get-response
    0x30, 0x54,                                     // SEQUENCE, length 84
    0x02, 0x01, 0x00,                               // INTEGER version-1 (0)
    0x04, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, // OCTET STRING "public"
    0xa2, 0x47,                                     // GetResponse-PDU, length 71
    0x02, 0x01, 0x26,                               // INTEGER request-id (38)
    0x02, 0x01, 0x00,                               // INTEGER error-status (0)
    0x02, 0x01, 0x00,                               // INTEGER error-index (0)
    0x30, 0x3c,                                     // SEQUENCE variable-bindings, length 60
    0x30, 0x3a,                                     // SEQUENCE VarBind, length 58
    0x06, 0x0a, 0x2b, 0x06, 0x01, 0x02, 0x01, 0x01, 0x02, 0x00, // OID 1.3.6.1.2.1.1.2.0
    0x06, 0x2c, 0x2b, 0x06, 0x01, 0x04, 0x01, 0x8f, 0x51, 0x01, // OID value (enterprise OID)
    0x01, 0x01, 0x02, 0x00, 0x8f, 0x51, 0x01, 0x02, 0x01, 0x01,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01
};

class SNMPAsn1Test : public ::testing::Test {
protected:
    void SetUp() override {
        alloc = ya_allocator_get_default();
        nxt_engine_config_t config = {.linkName = "eth", .trailerName = nullptr};
        engine = nxt_engine_create(&config);
        ASSERT_NE(engine, nullptr);
    }

    void TearDown() override {
        if (engine) {
            nxt_engine_destroy(engine);
        }
    }

    ya_allocator_t* alloc;
    nxt_engine_t* engine;
};

TEST_F(SNMPAsn1Test, ParseGetRequest) {
    // Create mbuf with SNMP GetRequest data
    nxt_mbuf_t* mbuf = nxt_mbuf_new_by_copy_wa(alloc, snmp_get_request_data, sizeof(snmp_get_request_data));
    ASSERT_NE(mbuf, nullptr);

    // Test SNMP dissection
    // Note: This tests the basic ASN.1 structure parsing

    // Verify the data length
    EXPECT_EQ(nxt_mbuf_get_length(mbuf), sizeof(snmp_get_request_data));

    // Verify first few bytes (SEQUENCE tag and length)
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 0), 0x30);  // SEQUENCE tag
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 1), 0x2e);  // Length 46

    // Verify version field
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 2), 0x02);  // INTEGER tag
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 3), 0x01);  // Length 1
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 4), 0x00);  // Version 0 (SNMPv1)

    // Verify community string
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 5), 0x04);  // OCTET STRING tag
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 6), 0x06);  // Length 6

    // Check community string "public"
    const char* community = (const char*)nxt_mbuf_get_raw(mbuf, 7);
    EXPECT_EQ(strncmp(community, "public", 6), 0);

    // Cleanup
    nxt_mbuf_free_wa(alloc, mbuf);
}

TEST_F(SNMPAsn1Test, ParseGetResponse) {
    // Create mbuf with SNMP GetResponse data
    nxt_mbuf_t* mbuf = nxt_mbuf_new_by_copy_wa(alloc, snmp_get_response_data, sizeof(snmp_get_response_data));
    ASSERT_NE(mbuf, nullptr);

    // Verify the data length
    EXPECT_EQ(nxt_mbuf_get_length(mbuf), sizeof(snmp_get_response_data));

    // Verify first few bytes (SEQUENCE tag and length)
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 0), 0x30);  // SEQUENCE tag
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 1), 0x54);  // Length 84

    // Verify version field
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 2), 0x02);  // INTEGER tag
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 3), 0x01);  // Length 1
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 4), 0x00);  // Version 0 (SNMPv1)

    // Verify PDU type (GetResponse = 0xa2)
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, 13), 0xa2); // GetResponse-PDU tag

    // Cleanup
    nxt_mbuf_free_wa(alloc, mbuf);
}

TEST_F(SNMPAsn1Test, ValidateOIDParsing) {
    // Test OID parsing from the GetRequest
    nxt_mbuf_t* mbuf = nxt_mbuf_new_by_copy_wa(alloc, snmp_get_request_data, sizeof(snmp_get_request_data));
    ASSERT_NE(mbuf, nullptr);

    // Navigate to the OID in the variable binding
    // Offset calculation: SEQUENCE(2) + version(3) + community(8) + GetRequest(2) + request-id(3) + error-status(3) + error-index(3) + varbindings(2) + varbind(2) = 28
    int oid_offset = 28;

    // Verify OID tag and length
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset), 0x06);     // OBJECT IDENTIFIER tag
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset + 1), 0x0a); // Length 10

    // Verify OID content (1.3.6.1.2.1.1.2.0)
    // First byte encodes first two arcs: 1*40 + 3 = 43 = 0x2b
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset + 2), 0x2b);
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset + 3), 0x06); // 6
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset + 4), 0x01); // 1
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset + 5), 0x02); // 2
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset + 6), 0x01); // 1
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset + 7), 0x01); // 1
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset + 8), 0x02); // 2
    EXPECT_EQ(nxt_mbuf_get_uint8(mbuf, oid_offset + 9), 0x00); // 0

    // Cleanup
    nxt_mbuf_free_wa(alloc, mbuf);
}

TEST_F(SNMPAsn1Test, ValidateRequestID) {
    // Test request ID parsing from both request and response
    nxt_mbuf_t* req_mbuf = nxt_mbuf_new_by_copy_wa(alloc, snmp_get_request_data, sizeof(snmp_get_request_data));
    nxt_mbuf_t* resp_mbuf = nxt_mbuf_new_by_copy_wa(alloc, snmp_get_response_data, sizeof(snmp_get_response_data));

    ASSERT_NE(req_mbuf, nullptr);
    ASSERT_NE(resp_mbuf, nullptr);

    // Request ID is at offset: SEQUENCE(2) + version(3) + community(8) + GetRequest(2) = 15
    int req_id_offset = 15;

    // Both should have the same request ID (38 = 0x26)
    EXPECT_EQ(nxt_mbuf_get_uint8(req_mbuf, req_id_offset), 0x02);   // INTEGER tag
    EXPECT_EQ(nxt_mbuf_get_uint8(req_mbuf, req_id_offset + 1), 0x01); // Length 1
    EXPECT_EQ(nxt_mbuf_get_uint8(req_mbuf, req_id_offset + 2), 0x26); // Value 38

    EXPECT_EQ(nxt_mbuf_get_uint8(resp_mbuf, req_id_offset), 0x02);   // INTEGER tag
    EXPECT_EQ(nxt_mbuf_get_uint8(resp_mbuf, req_id_offset + 1), 0x01); // Length 1
    EXPECT_EQ(nxt_mbuf_get_uint8(resp_mbuf, req_id_offset + 2), 0x26); // Value 38

    // Cleanup
    nxt_mbuf_free_wa(alloc, req_mbuf);
    nxt_mbuf_free_wa(alloc, resp_mbuf);
}

// Test for malformed SNMP data
TEST_F(SNMPAsn1Test, HandleMalformedData) {
    // Create mbuf with invalid SNMP data
    uint8_t invalid_data[] = {0x30, 0xff, 0x02, 0x01}; // Invalid length
    nxt_mbuf_t* mbuf = nxt_mbuf_new_by_copy_wa(alloc, invalid_data, sizeof(invalid_data));
    ASSERT_NE(mbuf, nullptr);

    // This should be handled gracefully by the dissector
    EXPECT_EQ(nxt_mbuf_get_length(mbuf), sizeof(invalid_data));

    // Cleanup
    nxt_mbuf_free_wa(alloc, mbuf);
}
