#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "Message.h"
#include "ber_decoder.h"

// Test data from our unit test
static const uint8_t test_data[] = {
    0x30, 0x2e, 0x02, 0x01, 0x00, 0x04, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
    0xa0, 0x21, 0x02, 0x01, 0x26, 0x02, 0x01, 0x00, 0x02, 0x01, 0x00, 0x30, 0x16,
    0x30, 0x14, 0x06, 0x0a, 0x2b, 0x06, 0x01, 0x02, 0x01, 0x01, 0x02, 0x00, 0x05, 0x00
};

// Real SNMP data from pcap
static const uint8_t real_data[] = {
    0x30, 0x26, 0x02, 0x01, 0x00, 0x04, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
    0xa0, 0x19, 0x02, 0x01, 0x26, 0x02, 0x01, 0x00, 0x02, 0x01, 0x00, 0x30, 0x0e,
    0x30, 0x0c, 0x06, 0x08, 0x2b, 0x06, 0x01, 0x02, 0x01, 0x01, 0x01, 0x00, 0x05, 0x00
};

void print_hex(const uint8_t *data, size_t len) {
    for (size_t i = 0; i < len; i++) {
        printf("%02x ", data[i]);
        if ((i + 1) % 16 == 0) printf("\n");
    }
    if (len % 16 != 0) printf("\n");
}

int main() {
    printf("Testing ASN.1 SNMP parser\n");
    printf("========================\n\n");

    // Test 1: Our test data
    printf("Test 1: Unit test data (%zu bytes)\n", sizeof(test_data));
    print_hex(test_data, sizeof(test_data));
    
    Message_t *message1 = NULL;
    asn_dec_rval_t result1 = ber_decode(0, &asn_DEF_Message, (void **)&message1, test_data, sizeof(test_data));
    
    printf("Result: code=%d, consumed=%zu\n", result1.code, result1.consumed);
    if (result1.code == RC_OK) {
        printf("SUCCESS: Message decoded successfully\n");
        printf("Version: %ld\n", message1->version);
        printf("Community: %.*s\n", (int)message1->community.size, message1->community.buf);
        ASN_STRUCT_FREE(asn_DEF_Message, message1);
    } else {
        printf("FAILED: Decoding failed\n");
    }
    printf("\n");

    // Test 2: Real pcap data
    printf("Test 2: Real pcap data (%zu bytes)\n", sizeof(real_data));
    print_hex(real_data, sizeof(real_data));
    
    Message_t *message2 = NULL;
    asn_dec_rval_t result2 = ber_decode(0, &asn_DEF_Message, (void **)&message2, real_data, sizeof(real_data));
    
    printf("Result: code=%d, consumed=%zu\n", result2.code, result2.consumed);
    if (result2.code == RC_OK) {
        printf("SUCCESS: Message decoded successfully\n");
        printf("Version: %ld\n", message2->version);
        printf("Community: %.*s\n", (int)message2->community.size, message2->community.buf);
        ASN_STRUCT_FREE(asn_DEF_Message, message2);
    } else {
        printf("FAILED: Decoding failed\n");
    }
    printf("\n");

    // Test 3: Manual BER parsing to understand the issue
    printf("Test 3: Manual BER analysis\n");
    printf("First few bytes of real data:\n");
    printf("0x30 = SEQUENCE tag\n");
    printf("0x26 = length 38 bytes\n");
    printf("0x02 = INTEGER tag\n");
    printf("0x01 = length 1 byte\n");
    printf("0x00 = version 0\n");
    printf("0x04 = OCTET STRING tag\n");
    printf("0x06 = length 6 bytes\n");
    printf("'public' = community string\n");
    printf("0xa0 = GetRequest-PDU tag (context-specific, constructed, tag 0)\n");
    printf("0x19 = length 25 bytes\n");

    return 0;
}
